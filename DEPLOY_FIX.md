# 🔧 Deno Deploy ISOLATE_INTERNAL_FAILURE 修复指南

## 🚨 问题分析

`ISOLATE_INTERNAL_FAILURE` 错误通常由以下原因引起：

1. **端口/主机名配置问题** - Deno Deploy 自动管理这些
2. **复杂的模块依赖** - 可能导致隔离环境失败
3. **不兼容的 API 调用** - 某些 Deno API 在云环境中受限
4. **内存或执行时间超限** - 复杂的正则表达式或大量计算

## ✅ 修复方案

### 方案 1：使用简化版本（推荐）

使用 `deploy-simple.ts` 文件，已移除可能导致问题的复杂功能：

```bash
# 部署简化版本
deployctl deploy --project=your-project-name deploy-simple.ts
```

**简化版本的改进：**
- ✅ 移除了端口和主机名配置
- ✅ 简化了错误处理逻辑
- ✅ 移除了图片代理功能（可能导致内存问题）
- ✅ 使用最简单的 `Deno.serve()` 调用

### 方案 2：进一步简化模块

如果方案 1 仍然失败，可以创建一个最小化版本：

```typescript
// minimal-deploy.ts
export default {
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    
    if (url.pathname === '/') {
      return new Response(JSON.stringify({
        name: '豆瓣电影爬虫 API',
        status: 'running',
        endpoints: ['/api/search']
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (url.pathname === '/api/search' && request.method === 'POST') {
      try {
        const body = await request.json();
        // 这里可以逐步添加搜索功能
        return new Response(JSON.stringify({
          success: true,
          message: '功能开发中',
          query: body.movieName
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: '请求格式错误'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
    
    return new Response('Not Found', { status: 404 });
  }
};
```

### 方案 3：检查模块问题

如果问题持续存在，可能是某个模块导致的。按以下顺序排查：

1. **检查 modules/parser.ts** - 包含复杂的正则表达式
2. **检查 modules/http.ts** - 网络请求可能超时
3. **检查 modules/search.ts** - 搜索逻辑可能过于复杂

## 🔍 调试步骤

### 1. 本地测试
```bash
# 测试简化版本
deno run --allow-net --allow-env deploy-simple.ts

# 检查是否有语法错误
deno check deploy-simple.ts
```

### 2. 逐步部署
```bash
# 先部署最小版本
deployctl deploy --project=test-project minimal-deploy.ts

# 成功后再尝试简化版本
deployctl deploy --project=test-project deploy-simple.ts
```

### 3. 查看部署日志
在 Deno Deploy 控制台查看详细的错误日志，寻找具体的失败原因。

## 🛠️ 常见解决方案

### 问题：模块导入失败
**解决方案：** 确保所有相对路径正确，使用 `./` 前缀

### 问题：网络请求超时
**解决方案：** 在 HTTP 客户端中设置更短的超时时间

### 问题：正则表达式过于复杂
**解决方案：** 简化 parser.ts 中的正则表达式

### 问题：内存使用过高
**解决方案：** 减少同时处理的数据量，优化字符串操作

## 📋 部署检查清单

- [ ] 移除了所有 `Deno.exit()` 调用
- [ ] 移除了端口和主机名配置
- [ ] 简化了错误处理逻辑
- [ ] 测试了本地运行
- [ ] 检查了模块导入路径
- [ ] 验证了 TypeScript 类型检查

## 🎯 推荐部署流程

1. **使用 deploy-simple.ts**
   ```bash
   deployctl deploy --project=douban-movie deploy-simple.ts
   ```

2. **如果失败，使用最小版本**
   ```bash
   # 创建 minimal-deploy.ts 并部署
   deployctl deploy --project=douban-movie minimal-deploy.ts
   ```

3. **逐步添加功能**
   - 先确保基础框架工作
   - 然后逐个添加搜索、解析等功能
   - 每次添加后都测试部署

## 🔗 有用的链接

- [Deno Deploy 文档](https://docs.deno.com/deploy/)
- [Deno Deploy 限制说明](https://docs.deno.com/deploy/api/)
- [deployctl 使用指南](https://github.com/denoland/deployctl)

---

**修复状态**: 🔄 进行中  
**推荐方案**: 使用 `deploy-simple.ts`  
**备用方案**: 使用最小化版本逐步构建

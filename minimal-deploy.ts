/**
 * 豆瓣电影爬虫 - 最小化 Deno Deploy 版本
 * 
 * 用于排查 ISOLATE_INTERNAL_FAILURE 问题的最简版本
 */

// 最基础的 CORS 头部
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Content-Type': 'application/json'
};

/**
 * 处理 HTTP 请求
 */
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // CORS 预检
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: CORS_HEADERS
    });
  }
  
  // 根路径 - API 信息
  if (pathname === '/') {
    const info = {
      name: '豆瓣电影爬虫 API',
      version: '1.0.0-minimal',
      status: 'running',
      endpoints: {
        'GET /': 'API 信息',
        'GET /health': '健康检查',
        'POST /api/search': '搜索电影（开发中）'
      },
      message: '最小化版本，用于测试 Deno Deploy 兼容性'
    };
    
    return new Response(JSON.stringify(info, null, 2), {
      headers: CORS_HEADERS
    });
  }
  
  // 健康检查
  if (pathname === '/health') {
    return new Response(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0-minimal'
    }), {
      headers: CORS_HEADERS
    });
  }
  
  // API 搜索端点（简化版）
  if (pathname === '/api/search' && request.method === 'POST') {
    try {
      const body = await request.json();
      const movieName = body.movieName;
      
      if (!movieName || typeof movieName !== 'string') {
        return new Response(JSON.stringify({
          success: false,
          error: '请提供有效的电影名称'
        }), {
          status: 400,
          headers: CORS_HEADERS
        });
      }
      
      // 模拟搜索结果（避免实际网络请求）
      const mockResult = {
        title: movieName,
        year: '2023',
        rating: '8.0',
        genres: ['剧情'],
        actors: ['演员1', '演员2'],
        poster: '暂无封面',
        summary: `这是电影《${movieName}》的模拟信息，用于测试 API 功能。`
      };
      
      return new Response(JSON.stringify({
        success: true,
        data: mockResult,
        message: '这是模拟数据，用于测试 Deno Deploy 兼容性'
      }), {
        headers: CORS_HEADERS
      });
      
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: '请求处理失败'
      }), {
        status: 500,
        headers: CORS_HEADERS
      });
    }
  }
  
  // 404 处理
  return new Response(JSON.stringify({
    success: false,
    error: 'Not Found',
    available_endpoints: ['/', '/health', '/api/search']
  }), {
    status: 404,
    headers: CORS_HEADERS
  });
}

/**
 * Deno Deploy 入口点
 * 使用最简单的配置
 */
console.log('🎬 豆瓣电影爬虫 - 最小化版本启动');

// 最简单的 Deno.serve() 调用
Deno.serve(handleRequest);

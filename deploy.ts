#!/usr/bin/env deno run --allow-net --allow-env --allow-read

/**
 * 豆瓣电影爬虫 - Deno Deploy 专用入口文件
 * 
 * 这个文件专门为 Deno Deploy 环境优化，移除了所有不兼容的 API 调用
 */

import { searchMovie, selectBestMatch } from './modules/search.ts';
import { getMovieInfo } from './modules/parser.ts';
import { DoubanScraperError, ErrorType, MovieInfo } from './types.ts';

/**
 * 服务器配置
 */
const SERVER_CONFIG = {
  port: 8000, // Deno Deploy 通常使用 8000 端口
  hostname: '0.0.0.0' // 在云环境中监听所有接口
};

/**
 * CORS 头部
 */
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Content-Type': 'application/json; charset=utf-8'
};

/**
 * 处理电影搜索请求
 * @param movieName 电影名称
 * @returns Promise<MovieInfo> 电影信息
 */
async function handleMovieSearch(movieName: string): Promise<MovieInfo> {
  try {
    console.log(`🔍 API请求 - 搜索电影: ${movieName}`);
    
    // 搜索电影
    const searchResults = await searchMovie(movieName);
    
    if (searchResults.length === 0) {
      throw new DoubanScraperError(
        ErrorType.NO_RESULTS,
        `未找到电影 "${movieName}" 的搜索结果`
      );
    }
    
    // 选择最佳匹配
    const bestMatch = selectBestMatch(searchResults, movieName);
    console.log(`📍 选择电影: ${bestMatch.title}`);
    
    // 获取详细信息
    const movieInfo = await getMovieInfo(bestMatch.url);
    
    console.log(`✅ 成功获取电影信息: ${movieInfo.title}`);
    return movieInfo;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`❌ 搜索失败: ${errorMessage}`);
    throw error;
  }
}

/**
 * 处理HTTP请求
 * @param request HTTP请求对象
 * @returns Promise<Response> HTTP响应
 */
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // 处理CORS预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: CORS_HEADERS
    });
  }
  
  // 根路径 - 返回 API 使用说明
  if (pathname === '/') {
    const apiDoc = {
      name: '豆瓣电影爬虫 API',
      version: '1.0.0',
      description: '获取豆瓣电影信息的 API 服务',
      endpoints: {
        'POST /api/search': {
          description: '搜索电影信息',
          body: {
            movieName: 'string - 电影名称'
          },
          example: {
            movieName: '阳光普照'
          }
        },
        'GET /api/image-proxy': {
          description: '图片代理服务',
          params: {
            url: 'string - 图片URL'
          }
        }
      },
      usage: {
        curl: 'curl -X POST https://your-deploy-url.deno.dev/api/search -H "Content-Type: application/json" -d \'{"movieName":"阳光普照"}\''
      }
    };
    
    return new Response(JSON.stringify(apiDoc, null, 2), {
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      }
    });
  }
  
  // 图片代理路由
  if (pathname.startsWith('/api/image-proxy')) {
    const imageUrl = url.searchParams.get('url');

    if (!imageUrl) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少图片URL参数'
      }), {
        status: 400,
        headers: CORS_HEADERS
      });
    }

    try {
      // 代理请求图片，设置正确的Referer
      const imageResponse = await fetch(imageUrl, {
        headers: {
          'Referer': 'https://movie.douban.com/',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      });

      if (!imageResponse.ok) {
        throw new Error(`图片请求失败: ${imageResponse.status}`);
      }

      const imageBuffer = await imageResponse.arrayBuffer();
      let contentType = imageResponse.headers.get('content-type') || 'image/jpeg';

      // 如果是webp格式，改为jpeg
      if (contentType.includes('webp')) {
        contentType = 'image/jpeg';
      }

      // 生成合适的文件名
      const imageUrlObj = new URL(imageUrl);
      const originalFilename = imageUrlObj.pathname.split('/').pop() || 'image';
      const filename = originalFilename.replace(/\.webp$/i, '.jpg');

      return new Response(imageBuffer, {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=86400', // 缓存1天
          'Content-Disposition': `inline; filename="${filename}"`,
          'Access-Control-Allow-Origin': '*'
        }
      });

    } catch (error) {
      console.error('图片代理错误:', error);
      return new Response('图片加载失败', {
        status: 500,
        headers: CORS_HEADERS
      });
    }
  }

  // API 路由
  if (pathname === '/api/search' && request.method === 'POST') {
    try {
      const body = await request.json();
      const movieName = body.movieName;
      
      if (!movieName || typeof movieName !== 'string') {
        return new Response(JSON.stringify({
          success: false,
          error: '请提供有效的电影名称'
        }), {
          status: 400,
          headers: CORS_HEADERS
        });
      }
      
      const movieInfo = await handleMovieSearch(movieName);
      
      return new Response(JSON.stringify({
        success: true,
        data: movieInfo
      }), {
        status: 200,
        headers: CORS_HEADERS
      });
      
    } catch (error) {
      let errorMessage = '搜索失败';
      let statusCode = 500;
      
      if (error instanceof DoubanScraperError) {
        errorMessage = error.message;
        switch (error.type) {
          case ErrorType.NO_RESULTS:
            statusCode = 404;
            break;
          case ErrorType.INVALID_ARGS:
            statusCode = 400;
            break;
          case ErrorType.NETWORK_ERROR:
            statusCode = 503;
            break;
          default:
            statusCode = 500;
        }
      }
      
      return new Response(JSON.stringify({
        success: false,
        error: errorMessage
      }), {
        status: statusCode,
        headers: CORS_HEADERS
      });
    }
  }
  
  // 404 处理
  return new Response(JSON.stringify({
    success: false,
    error: 'Not Found'
  }), {
    status: 404,
    headers: CORS_HEADERS
  });
}

/**
 * Deno Deploy 入口点
 * 使用 Deno.serve() 而不是手动启动服务器
 */
console.log(`🎬 豆瓣电影爬虫 - Deno Deploy 版本`);
console.log(`🚀 服务器启动中...`);
console.log('='.repeat(60));

// 在 Deno Deploy 中，使用 Deno.serve() 是推荐的方式
Deno.serve({
  port: SERVER_CONFIG.port,
  hostname: SERVER_CONFIG.hostname,
}, handleRequest);

console.log(`✅ 服务器已启动！`);

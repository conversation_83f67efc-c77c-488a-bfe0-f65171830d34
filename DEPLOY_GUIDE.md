# 豆瓣电影爬虫 - Deno Deploy 部署指南

## 🎯 修复完成

✅ **所有 Deno Deploy 兼容性问题已修复！**

### 修复内容总结

1. **移除了所有 `Deno.exit()` 调用**
   - `main.ts`: 第37、48、178、184、189行
   - `server.ts`: 第271行
   - 使用 `return` 和 `throw` 替代进程退出

2. **调整了信号处理器**
   - 移除了 `Deno.addSignalListener('SIGINT')` 调用
   - 在云环境中，进程生命周期由平台管理

3. **修复了 TypeScript 错误**
   - 修复了所有 `error.message` 的类型错误
   - 添加了 `version` 属性到 `CliArgs` 接口

4. **创建了专用的 Deno Deploy 入口文件**
   - `deploy.ts`: 专门为 Deno Deploy 优化的版本

## 🚀 部署方法

### 方法一：使用 deployctl（推荐）

1. **安装 deployctl**
   ```bash
   deno install -A jsr:@deno/deployctl --global
   ```

2. **部署到 Deno Deploy**
   ```bash
   deployctl deploy --project=douban-movie-scraper deploy.ts
   ```

### 方法二：通过 GitHub Actions

1. **创建 `.github/workflows/deploy.yml`**
   ```yaml
   name: Deploy to Deno Deploy
   on:
     push:
       branches: [main]
   
   jobs:
     deploy:
       name: Deploy
       runs-on: ubuntu-latest
       permissions:
         id-token: write
         contents: read
       steps:
         - name: Clone repository
           uses: actions/checkout@v4
         - name: Install Deno
           uses: denoland/setup-deno@v2
           with:
             deno-version: v2.x
         - name: Upload to Deno Deploy
           uses: denoland/deployctl@v1
           with:
             project: "douban-movie-scraper"
             entrypoint: "deploy.ts"
   ```

## 📁 文件说明

### 核心文件
- `deploy.ts` - **Deno Deploy 专用入口文件**（推荐用于部署）
- `main.ts` - CLI 版本（已修复，兼容 Deno Deploy）
- `server.ts` - 本地服务器版本（已修复）

### 配置文件
- `deno.json` - 已添加 deploy 相关任务
- `types.ts` - 已添加 version 属性

## 🔧 本地测试

### 测试 Deno Deploy 版本
```bash
deno run --allow-net --allow-env --allow-read deploy.ts
```

### 测试 CLI 版本
```bash
deno run --allow-net --allow-env main.ts -m "阳光普照"
```

### 测试本地服务器版本
```bash
deno run --allow-net --allow-env --allow-read server.ts
```

## 🌐 API 端点

部署后，您的应用将提供以下端点：

- `GET /` - API 文档和使用说明
- `POST /api/search` - 搜索电影信息
- `GET /api/image-proxy` - 图片代理服务

### API 使用示例

```bash
# 搜索电影
curl -X POST https://your-project.deno.dev/api/search \
  -H "Content-Type: application/json" \
  -d '{"movieName":"阳光普照"}'
```

## ⚠️ 重要说明

1. **环境变量**: 如果需要配置特殊的环境变量，请在 Deno Deploy 控制台中设置

2. **权限**: Deno Deploy 自动处理网络和环境变量权限

3. **限制**: 
   - 不支持文件系统写入
   - 不支持进程控制 API
   - 请求有时间和内存限制

4. **监控**: 可以在 Deno Deploy 控制台查看日志和性能指标

## 🎉 部署成功验证

部署成功后，访问您的部署 URL，应该看到 API 文档页面，包含：
- 应用名称和版本信息
- 可用端点列表
- 使用示例

## 🔍 故障排除

如果遇到问题：

1. **检查日志**: 在 Deno Deploy 控制台查看实时日志
2. **验证权限**: 确保代码中没有使用被禁止的 API
3. **测试本地**: 先在本地测试 `deploy.ts` 是否正常工作

---

**修复完成时间**: 2025-01-24  
**修复版本**: v1.1.0 (Deno Deploy 兼容版)  
**状态**: ✅ 可以部署

/**
 * 豆瓣电影爬虫 - Deno Deploy 简化版本
 * 
 * 专为 Deno Deploy 优化，移除了可能导致 ISOLATE_INTERNAL_FAILURE 的复杂功能
 */

import { searchMovie, selectBestMatch } from './modules/search.ts';
import { getMovieInfo } from './modules/parser.ts';
import { DoubanScraperError, ErrorType, MovieInfo } from './types.ts';

/**
 * CORS 头部
 */
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Content-Type': 'application/json; charset=utf-8'
};

/**
 * 处理电影搜索请求
 */
async function handleMovieSearch(movieName: string): Promise<MovieInfo> {
  try {
    console.log(`🔍 搜索电影: ${movieName}`);
    
    const searchResults = await searchMovie(movieName);
    
    if (searchResults.length === 0) {
      throw new DoubanScraperError(
        ErrorType.NO_RESULTS,
        `未找到电影 "${movieName}" 的搜索结果`
      );
    }
    
    const bestMatch = selectBestMatch(searchResults, movieName);
    console.log(`📍 选择电影: ${bestMatch.title}`);
    
    const movieInfo = await getMovieInfo(bestMatch.url);
    console.log(`✅ 获取成功: ${movieInfo.title}`);
    
    return movieInfo;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`❌ 搜索失败: ${errorMessage}`);
    throw error;
  }
}

/**
 * 处理HTTP请求
 */
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // 处理CORS预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: CORS_HEADERS
    });
  }
  
  // 根路径 - 返回简单的 API 说明
  if (pathname === '/') {
    const apiDoc = {
      name: '豆瓣电影爬虫 API',
      version: '1.0.0',
      description: '获取豆瓣电影信息的 API 服务',
      endpoints: {
        'POST /api/search': {
          description: '搜索电影信息',
          body: { movieName: 'string' },
          example: { movieName: '阳光普照' }
        }
      },
      usage: 'curl -X POST [URL]/api/search -H "Content-Type: application/json" -d \'{"movieName":"阳光普照"}\''
    };
    
    return new Response(JSON.stringify(apiDoc, null, 2), {
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      }
    });
  }

  // API 搜索路由
  if (pathname === '/api/search' && request.method === 'POST') {
    try {
      const body = await request.json();
      const movieName = body.movieName;
      
      if (!movieName || typeof movieName !== 'string') {
        return new Response(JSON.stringify({
          success: false,
          error: '请提供有效的电影名称'
        }), {
          status: 400,
          headers: CORS_HEADERS
        });
      }
      
      const movieInfo = await handleMovieSearch(movieName);
      
      return new Response(JSON.stringify({
        success: true,
        data: movieInfo
      }), {
        status: 200,
        headers: CORS_HEADERS
      });
      
    } catch (error) {
      let errorMessage = '搜索失败';
      let statusCode = 500;
      
      if (error instanceof DoubanScraperError) {
        errorMessage = error.message;
        switch (error.type) {
          case ErrorType.NO_RESULTS:
            statusCode = 404;
            break;
          case ErrorType.INVALID_ARGS:
            statusCode = 400;
            break;
          case ErrorType.NETWORK_ERROR:
            statusCode = 503;
            break;
          default:
            statusCode = 500;
        }
      }
      
      return new Response(JSON.stringify({
        success: false,
        error: errorMessage
      }), {
        status: statusCode,
        headers: CORS_HEADERS
      });
    }
  }
  
  // 健康检查路由
  if (pathname === '/health') {
    return new Response(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString()
    }), {
      headers: CORS_HEADERS
    });
  }
  
  // 404 处理
  return new Response(JSON.stringify({
    success: false,
    error: 'Not Found'
  }), {
    status: 404,
    headers: CORS_HEADERS
  });
}

/**
 * Deno Deploy 入口点
 */
console.log(`🎬 豆瓣电影爬虫 - Deno Deploy 简化版`);

// 使用最简单的 Deno.serve() 调用
Deno.serve(handleRequest);

/**
 * HTML解析和数据提取模块
 * 
 * 提供豆瓣电影详情页面的HTML解析功能，提取电影的各项信息
 */

import { MovieInfo, DoubanScraperError, ErrorType } from '../types.ts';
import { fetchText } from './http.ts';

/**
 * 电影信息解析器类
 */
export class MovieParser {

  /**
   * 解析电影信息
   * @param html 电影详情页面HTML
   * @returns MovieInfo 电影信息对象
   */
  parseMovieInfo(html: string): MovieInfo {
    try {
      // 在Deno中使用正则表达式和字符串解析代替DOMParser
      const doc = this.createDocumentFromHtml(html);

      if (!doc) {
        throw new Error('HTML解析失败');
      }

      const title = this.extractTitle(doc);
      const year = this.extractYear(doc);
      const rating = this.extractRating(doc);
      const genres = this.extractGenres(doc);
      const actors = this.extractActors(doc);
      const poster = this.extractPoster(doc);
      const summary = this.extractSummary(doc);

      // 调试日志
      console.log('🎬 解析结果:');
      console.log(`  标题: ${title}`);
      console.log(`  年份: ${year}`);
      console.log(`  评分: ${rating}`);
      console.log(`  类型: ${genres.join(', ')}`);
      console.log(`  主演: ${actors.join(', ')}`);
      console.log(`  封面: ${poster}`);
      console.log(`  简介: ${summary.substring(0, 100)}...`);

      return {
        title,
        year,
        rating,
        genres,
        actors,
        poster,
        summary
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new DoubanScraperError(
        ErrorType.PARSE_ERROR,
        `解析电影信息失败: ${errorMessage}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 创建简化的文档对象（使用正则表达式解析）
   * @param html HTML字符串
   * @returns 简化的文档对象
   */
  private createDocumentFromHtml(html: string): { html: string } {
    return { html };
  }

  /**
   * 提取电影标题
   * @param doc 简化的文档对象
   * @returns string 电影标题
   */
  private extractTitle(doc: { html: string }): string {
    const html = doc.html;

    // 尝试多种正则表达式模式
    const patterns = [
      /<h1[^>]*>.*?<span[^>]*property="v:itemreviewed"[^>]*>([^<]+)<\/span>/i,
      /<h1[^>]*>.*?<span[^>]*>([^<]+)<\/span>/i,
      /<h1[^>]*>([^<]+)<\/h1>/i,
      /property="v:itemreviewed"[^>]*>([^<]+)</i
    ];

    for (const pattern of patterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        let title = match[1].trim();
        // 移除年份信息（如果包含在标题中）
        title = title.replace(/\s*\(\d{4}\)\s*$/, '');
        if (title) {
          return title;
        }
      }
    }

    throw new Error('无法提取电影标题');
  }

  /**
   * 提取上映年份
   * @param doc 简化的文档对象
   * @returns string 上映年份
   */
  private extractYear(doc: { html: string }): string {
    const html = doc.html;

    // 尝试从标题中提取年份
    const titleMatch = html.match(/<h1[^>]*>.*?\((\d{4})\)/i);
    if (titleMatch) {
      return titleMatch[1];
    }

    // 尝试从上映日期中提取年份
    const releaseDateMatch = html.match(/property="v:initialReleaseDate"[^>]*>([^<]*(\d{4})[^<]*)</i);
    if (releaseDateMatch) {
      const yearMatch = releaseDateMatch[1].match(/(\d{4})/);
      if (yearMatch) {
        return yearMatch[1];
      }
    }

    // 从页面信息中查找年份
    const infoMatch = html.match(/<span[^>]*>(\d{4})<\/span>/);
    if (infoMatch) {
      return infoMatch[1];
    }

    return '未知';
  }

  /**
   * 提取豆瓣评分
   * @param doc 简化的文档对象
   * @returns string 豆瓣评分
   */
  private extractRating(doc: { html: string }): string {
    const html = doc.html;

    const patterns = [
      /class="rating_num"[^>]*>(\d+(?:\.\d+)?)</i,
      /property="v:average"[^>]*>(\d+(?:\.\d+)?)</i,
      /<strong[^>]*class="[^"]*rating_num[^"]*"[^>]*>(\d+(?:\.\d+)?)</i
    ];

    for (const pattern of patterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        const rating = match[1].trim();
        if (rating && /^\d+(\.\d+)?$/.test(rating)) {
          return rating;
        }
      }
    }

    return '暂无评分';
  }

  /**
   * 提取电影类型
   * @param doc 简化的文档对象
   * @returns string[] 电影类型列表
   */
  private extractGenres(doc: { html: string }): string[] {
    const html = doc.html;
    const genres: string[] = [];

    // 策略1: 从v:genre属性中提取
    const genrePattern = /property="v:genre"[^>]*>([^<]+)</gi;
    let match;
    while ((match = genrePattern.exec(html)) !== null) {
      const genre = match[1].trim();
      if (genre) {
        genres.push(genre);
      }
    }

    // 策略2: 从span标签中提取类型
    if (genres.length === 0) {
      const spanGenrePattern = /<span[^>]*>([^<]*(?:剧情|喜剧|动作|科幻|恐怖|爱情|犯罪|家庭|悬疑|惊悚|冒险|动画|传记|历史|战争|西部|音乐|歌舞|运动|纪录片)[^<]*)<\/span>/gi;
      while ((match = spanGenrePattern.exec(html)) !== null) {
        const genre = match[1].trim();
        if (genre && !genres.includes(genre)) {
          genres.push(genre);
        }
      }
    }

    // 策略3: 从信息区域提取
    if (genres.length === 0) {
      const infoPatterns = [
        /类型:\s*([^\n<]+)/i,
        /Genre:\s*([^\n<]+)/i,
        /类型[^>]*>([^<]+)</i
      ];

      for (const pattern of infoPatterns) {
        const infoMatch = html.match(pattern);
        if (infoMatch) {
          const genreText = infoMatch[1].trim();
          // 移除HTML标签
          const cleanText = genreText.replace(/<[^>]*>/g, '');
          const extractedGenres = cleanText.split(/[\/\s·]+/).filter(g => g.trim() && g.length > 1);
          genres.push(...extractedGenres);
          break;
        }
      }
    }

    return genres.length > 0 ? genres : ['未知'];
  }

  /**
   * 提取主演信息
   * @param doc 简化的文档对象
   * @returns string[] 主演列表
   */
  private extractActors(doc: { html: string }): string[] {
    const html = doc.html;
    const actors: string[] = [];

    // 策略1: 从主演区域提取完整的主演列表
    // 匹配主演区域的完整结构，包括所有嵌套的generic标签
    const mainActorPatterns = [
      // 模式1: 匹配主演后面的大段内容，直到遇到类型
      /主演[^:]*:\s*<[^>]*>(.*?)(?=<[^>]*>\s*类型|$)/is,
      // 模式2: 匹配主演后面的generic区域
      /主演[^:]*:\s*<[^>]*>(.*?)<\/[^>]*>\s*<[^>]*>\s*类型/is,
      // 模式3: 更宽泛的匹配，包含更多内容
      /主演[^:]*:(.*?)(?:类型|制片国家)/is,
      // 模式4: 基于浏览器快照的结构匹配
      /主演[^:]*:\s*<[^>]*>\s*<[^>]*>\s*<[^>]*>([^<]*)<\/[^>]*>\s*<[^>]*>\s*<[^>]*>([^<]*)<\/[^>]*>/is,
      // 模式5: 匹配多个generic标签中的链接
      /主演[^:]*:\s*<[^>]*>((?:<[^>]*>.*?<\/[^>]*>\s*){2,10})/is
    ];

    for (let i = 0; i < mainActorPatterns.length; i++) {
      const pattern = mainActorPatterns[i];
      const mainActorMatch = html.match(pattern);
      if (mainActorMatch) {
        const actorSection = mainActorMatch[1];
        console.log(`模式${i+1}匹配成功，主演区域HTML:`, actorSection.substring(0, 1000));

        // 从主演区域提取所有链接中的演员名称
        const actorLinkPattern = /<a[^>]*>([^<]+)<\/a>/g;
        let match;
        let linkCount = 0;
        while ((match = actorLinkPattern.exec(actorSection)) !== null) {
          linkCount++;
          const actor = match[1].trim();
          console.log(`找到演员链接${linkCount}: "${actor}"`);
          if (actor && actor !== '更多...' && actor !== '更多' && !actors.includes(actor)) {
            actors.push(actor);
          }
        }

        console.log(`模式${i+1}提取到${actors.length}个主演:`, actors);

        if (actors.length > 0) {
          break; // 如果找到了主演，就停止尝试其他模式
        }
      } else {
        console.log(`模式${i+1}未匹配`);
      }
    }

    // 策略1.5: 如果上面的方法没有找到，尝试更宽泛的匹配
    if (actors.length === 0) {
      // 匹配主演后面的所有generic标签
      const genericActorPattern = /主演[^:]*:.*?(<generic[^>]*>.*?<\/generic>.*?){5,}/is;
      const genericMatch = html.match(genericActorPattern);

      if (genericMatch) {
        const actorSection = genericMatch[0];
        console.log('Generic主演区域:', actorSection.substring(0, 500));

        const actorLinkPattern = /<a[^>]*>([^<]+)<\/a>/g;
        let match;
        while ((match = actorLinkPattern.exec(actorSection)) !== null) {
          const actor = match[1].trim();
          if (actor && actor !== '更多...' && actor !== '更多' && !actors.includes(actor)) {
            actors.push(actor);
          }
        }
      }
    }

    // 策略2: 如果第一种方法没有找到足够的主演，尝试其他模式
    if (actors.length < 2) {
      const patterns = [
        // 从演职员列表提取
        /class="actor"[^>]*>.*?<a[^>]*>([^<]+)<\/a>/gi,
        // 从celebrity列表提取
        /class="celebrity"[^>]*>.*?<a[^>]*class="name"[^>]*>([^<]+)<\/a>/gi,
        // 从演员链接中提取（更通用）
        /<a[^>]*href="[^"]*celebrity[^"]*"[^>]*>([^<]+)<\/a>/gi,
        // 从演员相关的div中提取
        /<div[^>]*class="[^"]*celebrity[^"]*"[^>]*>.*?<a[^>]*>([^<]+)<\/a>/gi
      ];

      // 尝试所有模式
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const actor = match[1].trim();
          if (actor && !actors.includes(actor)) {
            actors.push(actor);
          }
        }
        // 重置正则表达式的lastIndex
        pattern.lastIndex = 0;
      }
    }

    // 如果没有找到，尝试从信息区域的文本提取
    if (actors.length === 0) {
      // 更精确的主演信息提取
      const infoPatterns = [
        // 策略1: 匹配主演后面的完整结构
        /主演[^:]*:\s*<[^>]*>(.*?)<\/[^>]*>/is,
        // 策略2: 匹配主演后面的文本内容
        /主演[^:]*:\s*([^<\n]+)/i,
        // 策略3: 从generic标签中提取主演信息
        /<generic[^>]*>\s*主演[^:]*:\s*<\/[^>]*>\s*<[^>]*>(.*?)<\/[^>]*>/is,
        // 策略4: 更宽泛的主演匹配
        /主演[^>]*>([^<]+)</i,
        /演员[^>]*>([^<]+)</i
      ];

      for (const pattern of infoPatterns) {
        const infoMatch = html.match(pattern);
        if (infoMatch) {
          let actorText = infoMatch[1].trim();

          // 移除HTML标签但保留链接文本
          actorText = actorText.replace(/<a[^>]*>([^<]+)<\/a>/g, '$1');
          actorText = actorText.replace(/<[^>]*>/g, '');

          // 分割主演名称
          const extractedActors = actorText
            .split(/[\/\s·]+/)
            .map(actor => actor.trim())
            .filter(actor =>
              actor.length > 1 &&
              actor !== '更多...' &&
              actor !== '...' &&
              actor !== '更多' &&
              !actor.match(/^\d+$/) // 排除纯数字
            );

          if (extractedActors.length > 0) {
            actors.push(...extractedActors);
            break;
          }
        }
      }
    }

    // 清理和去重
    const cleanActors = actors
      .map(actor => actor.trim())
      .filter(actor => actor.length > 1 && actor !== '更多...' && actor !== '...')
      .slice(0, 8); // 限制最多8个主要演员

    console.log(`最终主演列表(${cleanActors.length}个):`, cleanActors);
    return cleanActors.length > 0 ? cleanActors : ['未知'];
  }

  /**
   * 提取电影封面
   * @param doc 简化的文档对象
   * @returns string 封面图片URL
   */
  private extractPoster(doc: { html: string }): string {
    const html = doc.html;

    const patterns = [
      // 策略1: 主要封面图片
      /id="mainpic"[^>]*>.*?<img[^>]*src="([^"]+)"/i,
      // 策略2: 带有v:image属性的图片
      /<img[^>]*rel="v:image"[^>]*src="([^"]+)"/i,
      // 策略3: 任何豆瓣图片服务器的图片
      /<img[^>]*src="([^"]*img\.douban\.com[^"]*)"/i,
      // 策略4: 海报相关的图片
      /<img[^>]*src="([^"]*poster[^"]*)"/i,
      // 策略5: 电影相关的图片（更宽泛的匹配）
      /<img[^>]*src="([^"]*douban[^"]*\.(jpg|jpeg|png|webp)[^"]*)"/i
    ];

    for (const pattern of patterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        let src = match[1];

        // 确保URL是完整的
        if (src.startsWith('//')) {
          src = 'https:' + src;
        } else if (src.startsWith('/')) {
          src = 'https://img.douban.com' + src;
        }

        // 确保使用高质量图片
        src = src.replace(/\/s_ratio_poster\//, '/l_ratio_poster/');
        src = src.replace(/\/m_ratio_poster\//, '/l_ratio_poster/');
        src = src.replace(/\/s\//, '/l/');
        src = src.replace(/\/m\//, '/l/');

        // 将webp格式转换为jpg
        src = src.replace(/\.webp$/i, '.jpg');

        // 使用图片代理来避免403错误
        const proxyUrl = `/api/image-proxy?url=${encodeURIComponent(src)}`;
        return proxyUrl;
      }
    }

    return '暂无封面';
  }

  /**
   * 提取电影简介
   * @param doc 简化的文档对象
   * @returns string 电影简介
   */
  private extractSummary(doc: { html: string }): string {
    const html = doc.html;

    const patterns = [
      // 策略1: 从span标签中提取简介
      /<span[^>]*property="v:summary"[^>]*>(.*?)<\/span>/is,
      // 策略2: 从简介相关的div中提取
      /<div[^>]*class="[^"]*intro[^"]*"[^>]*>(.*?)<\/div>/is,
      // 策略3: 从剧情简介区域提取
      /剧情简介[^>]*>.*?<span[^>]*>(.*?)<\/span>/is,
      // 策略4: 从related-info中提取
      /<div[^>]*class="[^"]*related-info[^"]*"[^>]*>.*?<span[^>]*>(.*?)<\/span>/is,
      // 策略5: 从summary相关的类中提取
      /<div[^>]*class="[^"]*summary[^"]*"[^>]*>(.*?)<\/div>/is
    ];

    for (const pattern of patterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        let summary = match[1].trim();

        // 清理HTML标签
        summary = summary.replace(/<[^>]*>/g, '');
        // 清理多余的空白字符
        summary = summary.replace(/\s+/g, ' ').trim();
        // 移除常见的无用文本
        summary = summary.replace(/^(剧情简介|简介|Synopsis)[:：]?\s*/i, '');

        if (summary.length > 20) {
          return summary;
        }
      }
    }

    return '暂无简介';
  }
}

/**
 * 获取并解析电影信息
 * @param movieUrl 电影详情页URL
 * @returns Promise<MovieInfo> 电影信息
 */
export async function getMovieInfo(movieUrl: string): Promise<MovieInfo> {
  try {
    console.log(`📄 获取电影详情: ${movieUrl}`);
    
    const html = await fetchText(movieUrl);
    const parser = new MovieParser();
    const movieInfo = parser.parseMovieInfo(html);
    
    console.log(`✅ 成功解析电影信息: ${movieInfo.title}`);
    return movieInfo;
    
  } catch (error) {
    if (error instanceof DoubanScraperError) {
      throw error;
    }
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new DoubanScraperError(
      ErrorType.PARSE_ERROR,
      `获取电影信息失败: ${errorMessage}`,
      error instanceof Error ? error : undefined
    );
  }
}

/**
 * 创建默认的电影解析器实例
 */
export const movieParser = new MovieParser();

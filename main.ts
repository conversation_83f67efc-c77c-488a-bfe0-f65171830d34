#!/usr/bin/env deno run --allow-net

/**
 * 豆瓣电影爬虫 - 主程序入口
 * 
 * 使用方法:
 *   deno run --allow-net main.ts -m "电影名称"
 *   deno run --allow-net main.ts --movie "阳光普照" --format json
 */

import { handleCliArgs } from './modules/cli.ts';
import { searchMovie, selectBestMatch } from './modules/search.ts';
import { getMovieInfo } from './modules/parser.ts';
import { displayMovie, showSuccess, showError, showWarning } from './modules/formatter.ts';
import { DoubanScraperError, ErrorType, CliArgs } from './types.ts';

/**
 * 应用版本信息
 */
const APP_VERSION = '1.0.0';
const APP_NAME = '豆瓣电影爬虫';

/**
 * 主程序函数
 */
async function main(): Promise<void> {
  try {
    // 显示启动信息
    console.log(`\n🎬 ${APP_NAME} v${APP_VERSION}`);
    console.log('='.repeat(50));
    
    // 解析命令行参数
    const args = handleCliArgs(Deno.args);
    
    // 如果参数处理返回null，说明需要退出程序（如显示帮助信息）
    if (!args) {
      return; // 在 Deno Deploy 中使用 return 而不是 Deno.exit()
    }
    
    // 执行电影搜索和信息获取
    await processMovieRequest(args);
    
    // 显示完成信息
    showSuccess('程序执行完成！');
    
  } catch (error) {
    await handleGlobalError(error);
    throw error; // 在 Deno Deploy 中使用 throw 而不是 Deno.exit()
  }
}

/**
 * 处理电影请求的主要流程
 * @param args 命令行参数
 */
async function processMovieRequest(args: CliArgs): Promise<void> {
  const movieName = args.movie!;
  
  try {
    // 第一步：搜索电影
    console.log(`\n🔍 正在搜索电影: "${movieName}"`);
    const searchResults = await searchMovie(movieName);
    
    if (searchResults.length === 0) {
      throw new DoubanScraperError(
        ErrorType.NO_RESULTS,
        `未找到电影 "${movieName}" 的搜索结果`
      );
    }
    
    // 第二步：选择最佳匹配结果
    const bestMatch = selectBestMatch(searchResults, movieName);
    console.log(`📍 选择电影: ${bestMatch.title}`);
    
    // 如果有多个结果，显示提示
    if (searchResults.length > 1) {
      showWarning(`找到 ${searchResults.length} 个结果，已自动选择最佳匹配`);
    }
    
    // 第三步：获取电影详细信息
    console.log(`\n📄 正在获取电影详情...`);
    const movieInfo = await getMovieInfo(bestMatch.url);
    
    // 第四步：显示结果
    console.log(`\n🎯 电影信息获取成功！`);
    displayMovie(movieInfo, {
      format: args.format || 'text',
      colorize: true,
      verbose: false
    });
    
  } catch (error) {
    if (error instanceof DoubanScraperError) {
      throw error;
    }
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new DoubanScraperError(
      ErrorType.UNKNOWN_ERROR,
      `处理电影请求时发生错误: ${errorMessage}`,
      error instanceof Error ? error : undefined
    );
  }
}

/**
 * 全局错误处理函数
 * @param error 错误对象
 */
async function handleGlobalError(error: unknown): Promise<void> {
  console.log('\n' + '='.repeat(50));
  
  if (error instanceof DoubanScraperError) {
    // 处理已知的应用错误
    switch (error.type) {
      case ErrorType.INVALID_ARGS:
        showError(`参数错误: ${error.message}`);
        console.log('\n💡 提示: 使用 --help 查看使用说明');
        break;
        
      case ErrorType.NO_RESULTS:
        showError(`搜索无结果: ${error.message}`);
        console.log('\n💡 建议:');
        console.log('  • 检查电影名称是否正确');
        console.log('  • 尝试使用电影的中文名称');
        console.log('  • 尝试使用更简短的关键词');
        break;
        
      case ErrorType.NETWORK_ERROR:
        showError(`网络错误: ${error.message}`);
        console.log('\n💡 建议:');
        console.log('  • 检查网络连接是否正常');
        console.log('  • 稍后重试');
        console.log('  • 检查是否被防火墙阻止');
        break;
        
      case ErrorType.PARSE_ERROR:
        showError(`解析错误: ${error.message}`);
        console.log('\n💡 建议:');
        console.log('  • 豆瓣网站可能已更新页面结构');
        console.log('  • 请联系开发者更新程序');
        break;
        
      default:
        showError(`未知错误: ${error.message}`);
        break;
    }
    
    // 在调试模式下显示详细错误信息
    if (Deno.env.get('DEBUG')) {
      console.log('\n🐛 调试信息:');
      console.log(`错误类型: ${error.type}`);
      if (error.originalError) {
        console.log(`原始错误: ${error.originalError.message}`);
        console.log(`堆栈跟踪: ${error.originalError.stack}`);
      }
    }
  } else {
    // 处理未知错误
    showError(`程序发生未知错误: ${error}`);
    console.log('\n💡 建议:');
    console.log('  • 请检查输入参数是否正确');
    console.log('  • 尝试重新运行程序');
    console.log('  • 如果问题持续存在，请联系开发者');
    
    // 在调试模式下显示完整错误信息
    if (Deno.env.get('DEBUG')) {
      console.error('\n🐛 完整错误信息:', error);
    }
  }
}

/**
 * 程序退出处理 - Deno Deploy 兼容版本
 */
function setupExitHandlers(): void {
  // 在 Deno Deploy 中，SIGINT 信号处理器不可用，跳过
  // Deno.addSignalListener 在无服务器环境中不适用

  // 处理未捕获的异常 - 在 Deno Deploy 中仍然有用
  globalThis.addEventListener('unhandledrejection', (event) => {
    console.error('\n❌ 未处理的Promise拒绝:', event.reason);
    // 在 Deno Deploy 中不使用 Deno.exit()，让错误自然传播
    event.preventDefault(); // 防止默认的错误处理
  });

  globalThis.addEventListener('error', (event) => {
    console.error('\n❌ 未捕获的错误:', event.error);
    // 在 Deno Deploy 中不使用 Deno.exit()，让错误自然传播
    event.preventDefault(); // 防止默认的错误处理
  });
}

/**
 * 程序入口点
 */
if (import.meta.main) {
  // 设置退出处理器
  setupExitHandlers();
  
  // 运行主程序
  await main();
}

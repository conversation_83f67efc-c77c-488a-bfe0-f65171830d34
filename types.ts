/**
 * 豆瓣电影爬虫 - 核心类型定义
 * 
 * 定义了电影信息、搜索结果、错误处理等核心数据结构
 */

/**
 * 电影基本信息接口
 */
export interface MovieInfo {
  /** 电影标题 */
  title: string;
  /** 上映年份 */
  year: string;
  /** 豆瓣评分 */
  rating: string;
  /** 电影类型列表 */
  genres: string[];
  /** 主演列表 */
  actors: string[];
  /** 电影封面图片URL */
  poster: string;
  /** 电影简介 */
  summary: string;
}

/**
 * 搜索结果接口
 */
export interface SearchResult {
  /** 电影ID */
  id: string;
  /** 电影详情页URL */
  url: string;
  /** 电影标题 */
  title: string;
}

/**
 * HTTP请求配置接口
 */
export interface HttpRequestOptions {
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 自定义请求头 */
  headers?: Record<string, string>;
}

/**
 * 命令行参数接口
 */
export interface CliArgs {
  /** 电影名称 */
  movie?: string;
  /** 显示帮助信息 */
  help?: boolean;
  /** 显示版本信息 */
  version?: boolean;
  /** 输出格式 */
  format?: 'text' | 'json';
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  /** 网络请求错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 解析错误 */
  PARSE_ERROR = 'PARSE_ERROR',
  /** 搜索无结果 */
  NO_RESULTS = 'NO_RESULTS',
  /** 参数错误 */
  INVALID_ARGS = 'INVALID_ARGS',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 自定义错误类
 */
export class DoubanScraperError extends Error {
  constructor(
    public type: ErrorType,
    message: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'DoubanScraperError';
  }
}

/**
 * 格式化输出选项
 */
export interface FormatOptions {
  /** 输出格式 */
  format: 'text' | 'json';
  /** 是否显示颜色 */
  colorize?: boolean;
  /** 是否显示详细信息 */
  verbose?: boolean;
}

/**
 * 应用配置接口
 */
export interface AppConfig {
  /** 默认User-Agent */
  userAgent: string;
  /** 请求超时时间 */
  timeout: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 豆瓣基础URL */
  doubanBaseUrl: string;
}
